# Reverse Infinite Scroll Test Plan

## Test Cases

### 1. Basic Functionality
- [ ] Create a new chatroom
- [ ] Send a few messages to populate the chat
- [ ] Scroll to the top of the message container
- [ ] Verify that "Loading more messages..." indicator appears
- [ ] Verify that older messages are loaded and displayed at the top
- [ ] Verify that scroll position is maintained (user stays viewing the same message)

### 2. Edge Cases
- [ ] Test with empty chatroom (no messages)
- [ ] Test when no more messages are available (should show "Beginning of conversation")
- [ ] Test rapid scrolling to top multiple times
- [ ] Test switching between chatrooms while loading

### 3. Performance
- [ ] Verify debouncing works (multiple scroll events don't trigger multiple loads)
- [ ] Check console logs for proper debugging information
- [ ] Verify no memory leaks or excessive re-renders

### 4. Error Handling
- [ ] Test error scenarios (if possible)
- [ ] Verify error toast appears on failure
- [ ] Verify loading state is properly reset after errors

## Expected Behavior

1. **Trigger Threshold**: Infinite scroll should trigger when scrollTop <= 150px
2. **Loading State**: Should show loading indicator at top of messages
3. **Scroll Restoration**: After loading, user should remain viewing the same content
4. **Debouncing**: Multiple rapid scroll events should be debounced to 150ms
5. **State Management**: Should use store's `isMessagesLoading` state consistently

## Console Logs to Watch For

- 🔄 Triggering infinite scroll load...
- ✅ Scroll position restored:
- ⚠️ No height difference detected after loading messages
- ❌ Failed to load more messages:
- 📄 No more messages to load
- ⏳ Already loading messages, skipping...
- 📭 No messages in current chatroom yet

## Fixed Issues

1. ✅ Consolidated loading states (removed duplicate `isLoadingMore`)
2. ✅ Improved scroll position restoration with double requestAnimationFrame
3. ✅ Enhanced trigger conditions with better thresholds
4. ✅ Added comprehensive logging and error handling
5. ✅ Added proper cleanup for timeouts
6. ✅ Added data-message-id attributes for better tracking
